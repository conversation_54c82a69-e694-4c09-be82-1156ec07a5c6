<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mẫu <PERSON><PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Calibri', sans-serif;
        }

        .form-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background-color: #f9fafb; /* bg-gray-50 */
            border-radius: 0.5rem; /* rounded-lg */
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); /* shadow-md */
        }
        .form-section h2 {
            font-size: 1.25rem; /* text-xl */
            font-weight: 600; /* font-semibold */
            margin-bottom: 1rem;
            color: #1f2937; /* text-gray-800 */
            border-bottom: 1px solid #e5e7eb; /* border-gray-200 */
            padding-bottom: 0.5rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500; /* font-medium */
            color: #374151; /* text-gray-700 */
        }
        input[type="text"],
        input[type="email"],
        input[type="tel"],
        input[type="date"],
        select,
        textarea {
            width: 100%;
            padding: 0.75rem;
            border: none; /* Loại bỏ border */
            border-radius: 0.375rem; /* rounded-md */
            background: transparent; /* Nền trong suốt */
            transition: background-color 0.15s ease-in-out;
        }
        input[type="text"]:focus,
        input[type="email"]:focus,
        input[type="tel"]:focus,
        input[type="date"]:focus,
        select:focus,
        textarea:focus {
            outline: none; /* Loại bỏ outline */
            background-color: rgba(59, 130, 246, 0.05); /* Nền nhạt khi focus */
        }
        textarea {
            min-height: 100px;
        }
        .grid-cols-custom {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }
    </style>
</head>
<body class="bg-gray-100 p-4 md:p-8">
    <div class="max-w-4xl mx-auto bg-white p-6 md:p-10 rounded-xl shadow-2xl">
        <header class="mb-8 text-center">
            <h1 class="text-3xl font-bold text-black">MẪU YÊU CẦU NGOẠI LỆ BẢO MẬT</h1>

        </header>

        <form id="securityExceptionForm">
            <section class="form-section">
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-black bg-white">
                        <thead>
                            <tr class="bg-gray-100">
                                <th colspan="4" class="border border-black px-4 py-2 text-left font-medium text-black">THÔNG TIN NGƯỜI YÊU CẦU</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="border border-black px-4 py-2 font-medium w-1/4">Họ và tên:</td>
                                <td class="border border-black px-4 py-2 w-1/4">
                                    <input type="text" id="requesterName" name="requesterName" required class="w-full p-1" placeholder="">
                                </td>
                                <td class="border border-black px-4 py-2 font-medium w-1/4">Phòng ban:</td>
                                <td class="border border-black px-4 py-2 w-1/4">
                                    <input type="text" id="department" name="department" required class="w-full p-1" placeholder="">
                                </td>
                            </tr>
                            <tr>
                                <td class="border border-black px-4 py-2 font-medium">Văn phòng:</td>
                                <td class="border border-black px-4 py-2">
                                    <input type="text" id="office" name="office" class="w-full p-1" placeholder="">
                                </td>
                                <td class="border border-black px-4 py-2 font-medium">Công ty:</td>
                                <td class="border border-black px-4 py-2">
                                    <input type="text" id="company" name="company" class="w-full p-1" placeholder="">
                                </td>
                            </tr>
                            <tr>
                                <td class="border border-black px-4 py-2 font-medium">Chức vụ:</td>
                                <td class="border border-black px-4 py-2">
                                    <input type="text" id="position" name="position" required class="w-full p-1" placeholder="">
                                </td>
                                <td class="border border-black px-4 py-2 font-medium">Email:</td>
                                <td class="border border-black px-4 py-2">
                                    <input type="email" id="email" name="email" required class="w-full p-1" placeholder="">
                                </td>
                            </tr>
                            <tr>
                                <td class="border border-black px-4 py-2 font-medium">Số điện thoại:</td>
                                <td class="border border-black px-4 py-2" colspan="3">
                                    <input type="tel" id="phone" name="phone" required class="w-full p-1" placeholder="">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <section class="form-section">
                <h2 class="text-lg font-semibold mb-4 text-gray-800">2. THÔNG TIN CHUNG VỀ LỖ HỔNG</h2>

                <div class="space-y-4">
                    <!-- Mã số tham chiếu -->
                    <div class="flex items-start">
                        <span class="text-lg mr-2 leading-6">•</span>
                        <div class="flex-1">
                            <label class="font-medium text-gray-700">Mã số tham chiếu (VulID/CVE):</label>
                            <input type="text" id="vulId" name="vulId" required class="w-full mt-1 p-2" placeholder="">
                        </div>
                    </div>

                    <!-- Môi trường ảnh hưởng -->
                    <div class="flex items-start">
                        <span class="text-lg mr-2 leading-6">•</span>
                        <div class="flex-1">
                            <label class="font-medium text-gray-700 block mb-2">Môi trường ảnh hưởng:</label>
                            <div class="flex flex-wrap gap-4">
                                <label class="flex items-center">
                                    <input type="checkbox" name="environment[]" value="Production" class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <span>Production</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="environment[]" value="Testing" class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <span>Testing</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="environment[]" value="Development" class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <span>Development</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <!-- Danh sách tài sản bị ảnh hưởng -->
                    <div class="flex items-start">
                        <span class="text-lg mr-2 leading-6">•</span>
                        <div class="flex-1">
                            <label class="font-medium text-gray-700 block mb-4">Danh sách các tài sản bị ảnh hưởng:</label>
                            <div class="overflow-x-auto">
                                <table class="w-full border-collapse border border-gray-300 bg-white">
                                    <thead>
                                        <tr class="bg-gray-100">
                                            <th class="border border-gray-300 px-4 py-2 text-left font-medium">Tên máy chủ/Tài sản</th>
                                            <th class="border border-gray-300 px-4 py-2 text-left font-medium">Địa chỉ IP</th>
                                            <th class="border border-gray-300 px-4 py-2 text-left font-medium">Phiên bản phần mềm</th>
                                            <th class="border border-gray-300 px-4 py-2 text-left font-medium">Ghi chú</th>
                                            <th class="border border-gray-300 px-4 py-2 text-center font-medium">Thao tác</th>
                                        </tr>
                                    </thead>
                                    <tbody id="assetsTableBody">
                                        <tr>
                                            <td class="border border-gray-300 px-2 py-1">
                                                <input type="text" name="assetName[]" placeholder="Ví dụ: Server Web App" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                                            </td>
                                            <td class="border border-gray-300 px-2 py-1">
                                                <input type="text" name="assetIP[]" placeholder="************" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                                            </td>
                                            <td class="border border-gray-300 px-2 py-1">
                                                <input type="text" name="assetVersion[]" placeholder="Apache 2.4.50" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                                            </td>
                                            <td class="border border-gray-300 px-2 py-1">
                                                <input type="text" name="assetNotes[]" placeholder="Ghi chú thêm" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                                            </td>
                                            <td class="border border-gray-300 px-2 py-1 text-center">
                                                <button type="button" onclick="removeAssetRow(this)" class="text-red-600 hover:text-red-800 font-medium">Xóa</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <button type="button" onclick="addAssetRow()" class="mt-3 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
                                + Thêm tài sản
                            </button>
                        </div>
                    </div>
                </div>
            </section>
                <div class="mt-6">
                    <h3 class="text-lg font-semibold mb-4 text-gray-800">3. MÔ TẢ CHI TIẾT LỖ HỔNG</h3>

                    <div class="space-y-4">
                        <!-- Tính chất lỗ hổng -->
                        <div class="flex items-start">
                            <span class="text-lg mr-2 leading-6">•</span>
                            <div class="flex-1">
                                <label class="font-medium text-gray-700">Tính chất lỗ hổng:</label>
                                <input type="text" name="vulnerabilityNature" class="w-full mt-1 p-2" placeholder="Nhập tính chất lỗ hổng...">
                            </div>
                        </div>

                        <!-- Tác động tiềm ẩn -->
                        <div class="flex items-start">
                            <span class="text-lg mr-2 leading-6">•</span>
                            <div class="flex-1">
                                <label class="font-medium text-gray-700">Tác động tiềm ẩn:</label>
                                <input type="text" name="potentialImpact" class="w-full mt-1 p-2" placeholder="Nhập tác động tiềm ẩn...">
                            </div>
                        </div>

                        <!-- Điểm CVSS -->
                        <div class="flex items-start">
                            <span class="text-lg mr-2 leading-6">•</span>
                            <div class="flex-1">
                                <label class="font-medium text-gray-700">Điểm CVSS:</label>
                                <input type="text" id="cvssScore" name="cvssScore" required class="w-full mt-1 p-2" placeholder="Ví dụ: 7.5">
                            </div>
                        </div>

                        <!-- Mức độ nghiêm trọng -->
                        <div class="flex items-start">
                            <span class="text-lg mr-2 leading-6">•</span>
                            <div class="flex-1">
                                <label class="font-medium text-gray-700 block mb-2">Mức độ nghiêm trọng:</label>
                                <div class="flex flex-wrap gap-4">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="severity[]" value="Low" class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span>Thấp</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="severity[]" value="Medium" class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span>Trung bình</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="severity[]" value="High" class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span>Cao</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="severity[]" value="Critical" class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span>Khẩn cấp</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="form-section">
                <h2 class="text-lg font-semibold mb-4 text-gray-800">4. LÝ DO KHÔNG THỂ KHẮC PHỤC NGAY</h2>

                <div class="space-y-4">
                    <!-- Nguyên nhân -->
                    <div class="flex items-start">
                        <span class="text-lg mr-2 leading-6">•</span>
                        <div class="flex-1">
                            <label class="font-medium text-gray-700">Nguyên nhân:</label>
                            <input type="text" name="rootCause" class="w-full mt-1 p-2" placeholder="">
                        </div>
                    </div>

                    <!-- Hạn chế kỹ thuật -->
                    <div class="flex items-start">
                        <span class="text-lg mr-2 leading-6">•</span>
                        <div class="flex-1">
                            <label class="font-medium text-gray-700">Hạn chế kỹ thuật:</label>
                            <input type="text" name="technicalLimitations" class="w-full mt-1 p-2" placeholder="">
                        </div>
                    </div>

                    <!-- Ảnh hưởng đến hệ thống nếu khắc phục ngay -->
                    <div class="flex items-start">
                        <span class="text-lg mr-2 leading-6">•</span>
                        <div class="flex-1">
                            <label class="font-medium text-gray-700">Ảnh hưởng đến hệ thống nếu khắc phục ngay:</label>
                            <input type="text" name="systemImpact" class="w-full mt-1 p-2" placeholder="">
                        </div>
                    </div>

                </div>
            </section>

            <section class="form-section">
                <h2 class="text-lg font-semibold mb-4 text-gray-800">5. THỜI HẠN NGOẠI LỆ YÊU CẦU</h2>

                <div class="space-y-4">
                    <!-- Từ ngày -->
                    <div class="flex items-start">
                        <span class="text-lg mr-2 leading-6">•</span>
                        <div class="flex-1">
                            <label class="font-medium text-gray-700">Từ ngày:</label>
                            <input type="date" name="exceptionStartDate" required class="w-full md:w-1/2 mt-1 p-2">
                        </div>
                    </div>

                    <!-- Đến ngày -->
                    <div class="flex items-start">
                        <span class="text-lg mr-2 leading-6">•</span>
                        <div class="flex-1">
                            <label class="font-medium text-gray-700">Đến ngày:</label>
                            <input type="date" id="exceptionExpiryDate" name="exceptionExpiryDate" required class="w-full md:w-1/2 mt-1 p-2">
                        </div>
                    </div>
                </div>
            </section>

            <section class="form-section">
                <h2 class="text-lg font-semibold mb-4 text-gray-800">6. KẾ HOẠCH KHẮC PHỤC DÀI HẠN</h2>

                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300 bg-white" id="remediationPlanTable">
                        <thead>
                            <tr class="bg-gray-100">
                                <th class="border border-gray-300 px-4 py-2 text-left font-medium">MỐC THỜI GIAN</th>
                                <th class="border border-gray-300 px-4 py-2 text-left font-medium">HÀNH ĐỘNG CỤ THỂ</th>
                                <th class="border border-gray-300 px-4 py-2 text-left font-medium">NGƯỜI PHỤ TRÁCH</th>
                                <th class="border border-gray-300 px-4 py-2 text-center font-medium w-20">⬇</th>
                            </tr>
                        </thead>
                        <tbody id="remediationPlanBody">
                            <tr>
                                <td class="border border-gray-300 px-2 py-1">
                                    <input type="text" name="timeline[]" placeholder="" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                                </td>
                                <td class="border border-gray-300 px-2 py-1">
                                    <input type="text" name="action[]" placeholder="" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                                </td>
                                <td class="border border-gray-300 px-2 py-1">
                                    <input type="text" name="responsible[]" placeholder="" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                                </td>
                                <td class="border border-gray-300 px-2 py-1 text-center">
                                    <button type="button" onclick="removeRemediationRow(this)" class="text-red-600 hover:text-red-800 font-medium">Xóa</button>
                                </td>
                            </tr>
                            <tr>
                                <td class="border border-gray-300 px-2 py-1">
                                    <input type="text" name="timeline[]" placeholder="" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                                </td>
                                <td class="border border-gray-300 px-2 py-1">
                                    <input type="text" name="action[]" placeholder="" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                                </td>
                                <td class="border border-gray-300 px-2 py-1">
                                    <input type="text" name="responsible[]" placeholder="" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                                </td>
                                <td class="border border-gray-300 px-2 py-1 text-center">
                                    <button type="button" onclick="removeRemediationRow(this)" class="text-red-600 hover:text-red-800 font-medium">Xóa</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <button type="button" onclick="addRemediationRow()" class="mt-3 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
                    + Thêm kế hoạch
                </button>
            </section>

            <section class="form-section">
                <h2 class="text-lg font-semibold mb-4 text-gray-800">7. BIỆN PHÁP KIỂM SOÁT BỔ SUNG</h2>

                <div class="space-y-4">
                    <!-- Đã triển khai -->
                    <div class="flex items-start">
                        <span class="text-lg mr-2 leading-6">•</span>
                        <div class="flex-1">
                            <label class="font-medium text-gray-700 block mb-2">Đã triển khai:</label>
                            <div class="ml-4">
                                <div class="flex items-start mb-2">
                                    <span class="text-lg mr-2 leading-6">•</span>
                                    <input type="text" name="implementedControls[]" class="w-full p-2" placeholder="">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Dự kiến triển khai -->
                    <div class="flex items-start">
                        <span class="text-lg mr-2 leading-6">•</span>
                        <div class="flex-1">
                            <label class="font-medium text-gray-700 block mb-2">Dự kiến triển khai:</label>
                            <div class="ml-4">
                                <div class="flex items-start mb-2">
                                    <span class="text-lg mr-2 leading-6">•</span>
                                    <input type="text" name="plannedControls[]" class="w-full p-2" placeholder="">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="form-section">
                <h2>8. Phê Duyệt</h2>
                <div>
                    <label for="assetOwnerApproval">Phê duyệt của chủ sở hữu tài sản:</label>
                    <div class="mt-2 p-4 border border-gray-300 rounded-md bg-gray-50">
                        <p class="text-sm text-gray-600 mb-2">Xác nhận rằng chủ sở hữu tài sản đã được thông báo và đồng ý với yêu cầu ngoại lệ này.</p>
                        <input type="text" id="assetOwnerName" name="assetOwnerName" placeholder="Tên chủ sở hữu tài sản" class="mb-2 w-full">
                        <input type="date" id="assetOwnerApprovalDate" name="assetOwnerApprovalDate" class="mb-2 w-full md:w-1/2">
                        <textarea id="assetOwnerComments" name="assetOwnerComments" rows="2" placeholder="Ghi chú của chủ sở hữu tài sản (nếu có)" class="mt-2 w-full"></textarea>
                    </div>
                </div>
                 <div class="mt-6">
                    <label for="securityLeadApproval">Phê duyệt của Trưởng bộ phận Bảo mật:</label>
                     <div class="mt-2 p-4 border border-gray-300 rounded-md bg-gray-50">
                        <p class="text-sm text-gray-600 mb-2">Xác nhận của Trưởng bộ phận Bảo mật.</p>
                        <input type="text" id="securityLeadName" name="securityLeadName" placeholder="Tên Trưởng bộ phận Bảo mật" class="mb-2 w-full">
                        <input type="date" id="securityLeadApprovalDate" name="securityLeadApprovalDate" class="mb-2 w-full md:w-1/2">
                        <textarea id="securityLeadComments" name="securityLeadComments" rows="2" placeholder="Ghi chú của Trưởng bộ phận Bảo mật (nếu có)" class="mt-2 w-full"></textarea>
                    </div>
                </div>
            </section>

            <div class="mt-10 text-right">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md transition duration-150 ease-in-out">
                    Gửi Yêu Cầu
                </button>
            </div>
        </form>
    </div>

    <script>
        // Hàm thêm hàng mới vào bảng tài sản
        function addAssetRow() {
            const tableBody = document.getElementById('assetsTableBody');
            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td class="border border-gray-300 px-2 py-1">
                    <input type="text" name="assetName[]" placeholder="Ví dụ: Server Web App" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                </td>
                <td class="border border-gray-300 px-2 py-1">
                    <input type="text" name="assetIP[]" placeholder="************" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                </td>
                <td class="border border-gray-300 px-2 py-1">
                    <input type="text" name="assetVersion[]" placeholder="Apache 2.4.50" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                </td>
                <td class="border border-gray-300 px-2 py-1">
                    <input type="text" name="assetNotes[]" placeholder="Ghi chú thêm" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                </td>
                <td class="border border-gray-300 px-2 py-1 text-center">
                    <button type="button" onclick="removeAssetRow(this)" class="text-red-600 hover:text-red-800 font-medium">Xóa</button>
                </td>
            `;
            tableBody.appendChild(newRow);
        }

        // Hàm xóa hàng khỏi bảng tài sản
        function removeAssetRow(button) {
            const tableBody = document.getElementById('assetsTableBody');
            // Chỉ cho phép xóa nếu còn nhiều hơn 1 hàng
            if (tableBody.children.length > 1) {
                button.closest('tr').remove();
            } else {
                alert('Phải có ít nhất một tài sản trong danh sách.');
            }
        }

        // Hàm thêm hàng mới vào bảng kế hoạch khắc phục
        function addRemediationRow() {
            const tableBody = document.getElementById('remediationPlanBody');
            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td class="border border-gray-300 px-2 py-1">
                    <input type="text" name="timeline[]" placeholder="" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                </td>
                <td class="border border-gray-300 px-2 py-1">
                    <input type="text" name="action[]" placeholder="" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                </td>
                <td class="border border-gray-300 px-2 py-1">
                    <input type="text" name="responsible[]" placeholder="" class="w-full border-0 p-1 focus:outline-none focus:ring-0 bg-transparent">
                </td>
                <td class="border border-gray-300 px-2 py-1 text-center">
                    <button type="button" onclick="removeRemediationRow(this)" class="text-red-600 hover:text-red-800 font-medium">Xóa</button>
                </td>
            `;
            tableBody.appendChild(newRow);
        }

        // Hàm xóa hàng khỏi bảng kế hoạch khắc phục
        function removeRemediationRow(button) {
            const tableBody = document.getElementById('remediationPlanBody');
            // Chỉ cho phép xóa nếu còn nhiều hơn 1 hàng
            if (tableBody.children.length > 1) {
                button.closest('tr').remove();
            } else {
                alert('Phải có ít nhất một kế hoạch trong danh sách.');
            }
        }

        // Script xử lý form
        const form = document.getElementById('securityExceptionForm');
        form.addEventListener('submit', function(event) {
            event.preventDefault(); // Ngăn chặn việc gửi form theo cách truyền thống

            // Thu thập dữ liệu từ bảng tài sản
            const assetNames = Array.from(document.querySelectorAll('input[name="assetName[]"]')).map(input => input.value);
            const assetIPs = Array.from(document.querySelectorAll('input[name="assetIP[]"]')).map(input => input.value);
            const assetVersions = Array.from(document.querySelectorAll('input[name="assetVersion[]"]')).map(input => input.value);
            const assetNotes = Array.from(document.querySelectorAll('input[name="assetNotes[]"]')).map(input => input.value);

            // Tạo danh sách tài sản
            const affectedAssets = [];
            for (let i = 0; i < assetNames.length; i++) {
                if (assetNames[i] || assetIPs[i] || assetVersions[i]) {
                    affectedAssets.push({
                        name: assetNames[i],
                        ip: assetIPs[i],
                        version: assetVersions[i],
                        notes: assetNotes[i]
                    });
                }
            }

            // Xử lý dữ liệu form
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            data.affectedAssets = affectedAssets;

            console.log('Dữ liệu form:', data);
            alert('Yêu cầu đã được gửi (xem console để biết chi tiết dữ liệu).');
        });
    </script>
</body>
</html>
