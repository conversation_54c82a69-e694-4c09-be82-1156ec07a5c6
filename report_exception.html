<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mẫu <PERSON><PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        /* Thêm font Inter nếu chưa có */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

        .form-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background-color: #f9fafb; /* bg-gray-50 */
            border-radius: 0.5rem; /* rounded-lg */
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); /* shadow-md */
        }
        .form-section h2 {
            font-size: 1.25rem; /* text-xl */
            font-weight: 600; /* font-semibold */
            margin-bottom: 1rem;
            color: #1f2937; /* text-gray-800 */
            border-bottom: 1px solid #e5e7eb; /* border-gray-200 */
            padding-bottom: 0.5rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500; /* font-medium */
            color: #374151; /* text-gray-700 */
        }
        input[type="text"],
        input[type="email"],
        input[type="tel"],
        select,
        textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db; /* border-gray-300 */
            border-radius: 0.375rem; /* rounded-md */
            box-shadow: inset 0 1px 2px 0 rgba(0, 0, 0, 0.05); /* shadow-sm */
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        input[type="text"]:focus,
        input[type="email"]:focus,
        input[type="tel"]:focus,
        select:focus,
        textarea:focus {
            border-color: #2563eb; /* focus:border-blue-500 */
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25); /* focus:ring-blue-500 focus:ring-opacity-50 */
        }
        textarea {
            min-height: 100px;
        }
        .grid-cols-custom {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }
    </style>
</head>
<body class="bg-gray-100 p-4 md:p-8">
    <div class="max-w-4xl mx-auto bg-white p-6 md:p-10 rounded-xl shadow-2xl">
        <header class="mb-8 text-center">
            <h1 class="text-3xl font-bold text-blue-700">MẪU YÊU CẦU NGOẠI LỆ BẢO MẬT</h1>
            <p class="text-gray-600 mt-2">Tài liệu chính thức để yêu cầu ngoại lệ cho một lỗ hổng bảo mật không thể khắc phục ngay lập tức.</p>
        </header>

        <form id="securityExceptionForm">
            <section class="form-section">
                <h2>1. Thông Tin Người Yêu Cầu</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="requesterName">Họ và tên:</label>
                        <input type="text" id="requesterName" name="requesterName" required>
                    </div>
                    <div>
                        <label for="department">Phòng ban:</label>
                        <input type="text" id="department" name="department" required>
                    </div>
                    <div>
                        <label for="position">Chức vụ:</label>
                        <input type="text" id="position" name="position" required>
                    </div>
                    <div>
                        <label for="email">Email:</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div>
                        <label for="phone">Số điện thoại:</label>
                        <input type="tel" id="phone" name="phone" required>
                    </div>
                </div>
            </section>

            <section class="form-section">
                <h2>2. Thông Tin Lỗ Hổng</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="vulId">Mã số tham chiếu lỗ hổng (VulID/CVE):</label>
                        <input type="text" id="vulId" name="vulId" required placeholder="Ví dụ: CVE-2023-12345">
                    </div>
                    <div>
                        <label for="environment">Môi trường ảnh hưởng:</label>
                        <select id="environment" name="environment" required>
                            <option value="">-- Chọn môi trường --</option>
                            <option value="Production">Production</option>
                            <option value="Testing">Testing</option>
                            <option value="Development">Development</option>
                        </select>
                    </div>
                </div>
                <div class="mt-6">
                    <label for="affectedAssets">Danh sách các tài sản bị ảnh hưởng (tên máy chủ, IP, phiên bản phần mềm):</label>
                    <textarea id="affectedAssets" name="affectedAssets" rows="4" required placeholder="Ví dụ: Server Web App (************, Apache 2.4.50), DB Server (********, MySQL 8.0)"></textarea>
                </div>
                <div class="mt-6">
                    <label for="vulnerabilityDescription">Mô tả chi tiết về lỗ hổng:</label>
                    <textarea id="vulnerabilityDescription" name="vulnerabilityDescription" rows="4" required></textarea>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div>
                        <label for="cvssScore">Điểm CVSS:</label>
                        <input type="text" id="cvssScore" name="cvssScore" required placeholder="Ví dụ: 7.5">
                    </div>
                    <div>
                        <label for="severity">Mức độ nghiêm trọng:</label>
                        <select id="severity" name="severity" required>
                            <option value="">-- Chọn mức độ --</option>
                            <option value="Critical">Rất Cao (Critical)</option>
                            <option value="High">Cao (High)</option>
                            <option value="Medium">Trung Bình (Medium)</option>
                            <option value="Low">Thấp (Low)</option>
                            <option value="Informational">Thông Tin (Informational)</option>
                        </select>
                    </div>
                </div>
            </section>

            <section class="form-section">
                <h2>3. Lý Do Yêu Cầu Ngoại Lệ và Kế Hoạch</h2>
                <div class="mt-6">
                    <label for="reasonForException">Lý do không thể khắc phục ngay lập tức:</label>
                    <textarea id="reasonForException" name="reasonForException" rows="4" required></textarea>
                </div>
                <div class="mt-6">
                    <label for="compensatingControls">Các biện pháp kiểm soát bổ sung đã/sẽ được triển khai:</label>
                    <textarea id="compensatingControls" name="compensatingControls" rows="4" required placeholder="Ví dụ: Tăng cường giám sát log, giới hạn truy cập mạng, áp dụng WAF rule tạm thời..."></textarea>
                </div>
                <div class="mt-6">
                    <label for="longTermRemediationPlan">Kế hoạch khắc phục dài hạn và mốc thời gian dự kiến:</label>
                    <textarea id="longTermRemediationPlan" name="longTermRemediationPlan" rows="4" required placeholder="Ví dụ: Nâng cấp hệ điều hành vào Q4/2024, Patch phần mềm sau khi nhà cung cấp phát hành bản vá (dự kiến 30/06/2024)..."></textarea>
                </div>
                <div class="mt-6">
                    <label for="exceptionExpiryDate">Thời hạn ngoại lệ yêu cầu (ngày):</label>
                    <input type="date" id="exceptionExpiryDate" name="exceptionExpiryDate" required class="w-full md:w-1/2">
                </div>
            </section>

            <section class="form-section">
                <h2>4. Phê Duyệt</h2>
                <div>
                    <label for="assetOwnerApproval">Phê duyệt của chủ sở hữu tài sản:</label>
                    <div class="mt-2 p-4 border border-gray-300 rounded-md bg-gray-50">
                        <p class="text-sm text-gray-600 mb-2">Xác nhận rằng chủ sở hữu tài sản đã được thông báo và đồng ý với yêu cầu ngoại lệ này.</p>
                        <input type="text" id="assetOwnerName" name="assetOwnerName" placeholder="Tên chủ sở hữu tài sản" class="mb-2">
                        <input type="date" id="assetOwnerApprovalDate" name="assetOwnerApprovalDate" class="mb-2 w-full md:w-1/2">
                        <textarea id="assetOwnerComments" name="assetOwnerComments" rows="2" placeholder="Ghi chú của chủ sở hữu tài sản (nếu có)" class="mt-2"></textarea>
                    </div>
                </div>
                 <div class="mt-6">
                    <label for="securityLeadApproval">Phê duyệt của Trưởng bộ phận Bảo mật:</label>
                     <div class="mt-2 p-4 border border-gray-300 rounded-md bg-gray-50">
                        <p class="text-sm text-gray-600 mb-2">Xác nhận của Trưởng bộ phận Bảo mật.</p>
                        <input type="text" id="securityLeadName" name="securityLeadName" placeholder="Tên Trưởng bộ phận Bảo mật" class="mb-2">
                        <input type="date" id="securityLeadApprovalDate" name="securityLeadApprovalDate" class="mb-2 w-full md:w-1/2">
                        <textarea id="securityLeadComments" name="securityLeadComments" rows="2" placeholder="Ghi chú của Trưởng bộ phận Bảo mật (nếu có)" class="mt-2"></textarea>
                    </div>
                </div>
            </section>

            <div class="mt-10 text-right">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md transition duration-150 ease-in-out">
                    Gửi Yêu Cầu
                </button>
            </div>
        </form>
    </div>

    <script>
        // Script có thể được thêm ở đây để xử lý form, ví dụ: gửi dữ liệu qua AJAX
        // Hoặc các tương tác UI phức tạp hơn.
        const form = document.getElementById('securityExceptionForm');
        form.addEventListener('submit', function(event) {
            event.preventDefault(); // Ngăn chặn việc gửi form theo cách truyền thống
            // Xử lý dữ liệu form ở đây
            // Ví dụ: thu thập dữ liệu và gửi đi
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            console.log('Dữ liệu form:', data);
            // Đây là nơi bạn sẽ gửi dữ liệu đến server hoặc xử lý nó
            alert('Yêu cầu đã được gửi (xem console để biết chi tiết dữ liệu).');
            // form.reset(); // Reset form sau khi gửi (tùy chọn)
        });
    </script>
</body>
</html>
