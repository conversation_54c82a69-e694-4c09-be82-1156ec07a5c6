<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mẫu <PERSON><PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Calibri', sans-serif;
        }

        .form-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background-color: #f9fafb; /* bg-gray-50 */
            border-radius: 0.5rem; /* rounded-lg */
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); /* shadow-md */
        }
        .form-section h2 {
            font-size: 1.25rem; /* text-xl */
            font-weight: 600; /* font-semibold */
            margin-bottom: 1rem;
            color: #1f2937; /* text-gray-800 */
            border-bottom: 1px solid #e5e7eb; /* border-gray-200 */
            padding-bottom: 0.5rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500; /* font-medium */
            color: #374151; /* text-gray-700 */
        }
        input[type="text"],
        input[type="email"],
        input[type="tel"],
        select,
        textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db; /* border-gray-300 */
            border-radius: 0.375rem; /* rounded-md */
            box-shadow: inset 0 1px 2px 0 rgba(0, 0, 0, 0.05); /* shadow-sm */
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        input[type="text"]:focus,
        input[type="email"]:focus,
        input[type="tel"]:focus,
        select:focus,
        textarea:focus {
            border-color: #2563eb; /* focus:border-blue-500 */
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25); /* focus:ring-blue-500 focus:ring-opacity-50 */
        }
        textarea {
            min-height: 100px;
        }
        .grid-cols-custom {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }
    </style>
</head>
<body class="bg-gray-100 p-4 md:p-8">
    <div class="max-w-4xl mx-auto bg-white p-6 md:p-10 rounded-xl shadow-2xl">
        <header class="mb-8 text-center">
            <h1 class="text-3xl font-bold text-black">MẪU YÊU CẦU NGOẠI LỆ BẢO MẬT</h1>

        </header>

        <form id="securityExceptionForm">
            <section class="form-section">
                <h2>1. Thông Tin Người Yêu Cầu</h2>
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300 bg-white">
                        <thead>
                            <tr class="bg-gray-100">
                                <th class="border border-gray-300 px-4 py-2 text-left font-medium">Họ và tên</th>
                                <th class="border border-gray-300 px-4 py-2 text-left font-medium">Phòng ban</th>
                                <th class="border border-gray-300 px-4 py-2 text-left font-medium">Chức vụ</th>
                                <th class="border border-gray-300 px-4 py-2 text-left font-medium">Email</th>
                                <th class="border border-gray-300 px-4 py-2 text-left font-medium">Số điện thoại</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="border border-gray-300 px-2 py-1">
                                    <input type="text" id="requesterName" name="requesterName" required class="w-full border-0 p-1 focus:ring-1 focus:ring-blue-500" placeholder="Nhập họ và tên">
                                </td>
                                <td class="border border-gray-300 px-2 py-1">
                                    <input type="text" id="department" name="department" required class="w-full border-0 p-1 focus:ring-1 focus:ring-blue-500" placeholder="Nhập phòng ban">
                                </td>
                                <td class="border border-gray-300 px-2 py-1">
                                    <input type="text" id="position" name="position" required class="w-full border-0 p-1 focus:ring-1 focus:ring-blue-500" placeholder="Nhập chức vụ">
                                </td>
                                <td class="border border-gray-300 px-2 py-1">
                                    <input type="email" id="email" name="email" required class="w-full border-0 p-1 focus:ring-1 focus:ring-blue-500" placeholder="<EMAIL>">
                                </td>
                                <td class="border border-gray-300 px-2 py-1">
                                    <input type="tel" id="phone" name="phone" required class="w-full border-0 p-1 focus:ring-1 focus:ring-blue-500" placeholder="0123456789">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <section class="form-section">
                <h2>2. Thông Tin Lỗ Hổng</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="vulId">Mã số tham chiếu lỗ hổng (VulID/CVE):</label>
                        <input type="text" id="vulId" name="vulId" required placeholder="Ví dụ: CVE-2023-12345">
                    </div>
                    <div>
                        <label for="environment">Môi trường ảnh hưởng:</label>
                        <select id="environment" name="environment" required>
                            <option value="">-- Chọn môi trường --</option>
                            <option value="Production">Production</option>
                            <option value="Testing">Testing</option>
                            <option value="Development">Development</option>
                        </select>
                    </div>
                </div>
                <div class="mt-6">
                    <label class="block mb-4">Danh sách các tài sản bị ảnh hưởng:</label>
                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse border border-gray-300 bg-white">
                            <thead>
                                <tr class="bg-gray-100">
                                    <th class="border border-gray-300 px-4 py-2 text-left font-medium">Tên máy chủ/Tài sản</th>
                                    <th class="border border-gray-300 px-4 py-2 text-left font-medium">Địa chỉ IP</th>
                                    <th class="border border-gray-300 px-4 py-2 text-left font-medium">Phiên bản phần mềm</th>
                                    <th class="border border-gray-300 px-4 py-2 text-left font-medium">Ghi chú</th>
                                    <th class="border border-gray-300 px-4 py-2 text-center font-medium">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody id="assetsTableBody">
                                <tr>
                                    <td class="border border-gray-300 px-2 py-1">
                                        <input type="text" name="assetName[]" placeholder="Ví dụ: Server Web App" class="w-full border-0 p-1 focus:ring-1 focus:ring-blue-500">
                                    </td>
                                    <td class="border border-gray-300 px-2 py-1">
                                        <input type="text" name="assetIP[]" placeholder="************" class="w-full border-0 p-1 focus:ring-1 focus:ring-blue-500">
                                    </td>
                                    <td class="border border-gray-300 px-2 py-1">
                                        <input type="text" name="assetVersion[]" placeholder="Apache 2.4.50" class="w-full border-0 p-1 focus:ring-1 focus:ring-blue-500">
                                    </td>
                                    <td class="border border-gray-300 px-2 py-1">
                                        <input type="text" name="assetNotes[]" placeholder="Ghi chú thêm" class="w-full border-0 p-1 focus:ring-1 focus:ring-blue-500">
                                    </td>
                                    <td class="border border-gray-300 px-2 py-1 text-center">
                                        <button type="button" onclick="removeAssetRow(this)" class="text-red-600 hover:text-red-800 font-medium">Xóa</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <button type="button" onclick="addAssetRow()" class="mt-3 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
                        + Thêm tài sản
                    </button>
                </div>
                <div class="mt-6">
                    <label for="vulnerabilityDescription">Mô tả chi tiết về lỗ hổng:</label>
                    <textarea id="vulnerabilityDescription" name="vulnerabilityDescription" rows="4" required></textarea>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div>
                        <label for="cvssScore">Điểm CVSS:</label>
                        <input type="text" id="cvssScore" name="cvssScore" required placeholder="Ví dụ: 7.5">
                    </div>
                    <div>
                        <label for="severity">Mức độ nghiêm trọng:</label>
                        <select id="severity" name="severity" required>
                            <option value="">-- Chọn mức độ --</option>
                            <option value="Critical">Rất Cao (Critical)</option>
                            <option value="High">Cao (High)</option>
                            <option value="Medium">Trung Bình (Medium)</option>
                            <option value="Low">Thấp (Low)</option>
                            <option value="Informational">Thông Tin (Informational)</option>
                        </select>
                    </div>
                </div>
            </section>

            <section class="form-section">
                <h2>3. Lý Do Yêu Cầu Ngoại Lệ và Kế Hoạch</h2>
                <div class="mt-6">
                    <label for="reasonForException">Lý do không thể khắc phục ngay lập tức:</label>
                    <textarea id="reasonForException" name="reasonForException" rows="4" required></textarea>
                </div>
                <div class="mt-6">
                    <label for="compensatingControls">Các biện pháp kiểm soát bổ sung đã/sẽ được triển khai:</label>
                    <textarea id="compensatingControls" name="compensatingControls" rows="4" required placeholder="Ví dụ: Tăng cường giám sát log, giới hạn truy cập mạng, áp dụng WAF rule tạm thời..."></textarea>
                </div>
                <div class="mt-6">
                    <label for="longTermRemediationPlan">Kế hoạch khắc phục dài hạn và mốc thời gian dự kiến:</label>
                    <textarea id="longTermRemediationPlan" name="longTermRemediationPlan" rows="4" required placeholder="Ví dụ: Nâng cấp hệ điều hành vào Q4/2024, Patch phần mềm sau khi nhà cung cấp phát hành bản vá (dự kiến 30/06/2024)..."></textarea>
                </div>
                <div class="mt-6">
                    <label for="exceptionExpiryDate">Thời hạn ngoại lệ yêu cầu (ngày):</label>
                    <input type="date" id="exceptionExpiryDate" name="exceptionExpiryDate" required class="w-full md:w-1/2">
                </div>
            </section>

            <section class="form-section">
                <h2>4. Phê Duyệt</h2>
                <div>
                    <label for="assetOwnerApproval">Phê duyệt của chủ sở hữu tài sản:</label>
                    <div class="mt-2 p-4 border border-gray-300 rounded-md bg-gray-50">
                        <p class="text-sm text-gray-600 mb-2">Xác nhận rằng chủ sở hữu tài sản đã được thông báo và đồng ý với yêu cầu ngoại lệ này.</p>
                        <input type="text" id="assetOwnerName" name="assetOwnerName" placeholder="Tên chủ sở hữu tài sản" class="mb-2">
                        <input type="date" id="assetOwnerApprovalDate" name="assetOwnerApprovalDate" class="mb-2 w-full md:w-1/2">
                        <textarea id="assetOwnerComments" name="assetOwnerComments" rows="2" placeholder="Ghi chú của chủ sở hữu tài sản (nếu có)" class="mt-2"></textarea>
                    </div>
                </div>
                 <div class="mt-6">
                    <label for="securityLeadApproval">Phê duyệt của Trưởng bộ phận Bảo mật:</label>
                     <div class="mt-2 p-4 border border-gray-300 rounded-md bg-gray-50">
                        <p class="text-sm text-gray-600 mb-2">Xác nhận của Trưởng bộ phận Bảo mật.</p>
                        <input type="text" id="securityLeadName" name="securityLeadName" placeholder="Tên Trưởng bộ phận Bảo mật" class="mb-2">
                        <input type="date" id="securityLeadApprovalDate" name="securityLeadApprovalDate" class="mb-2 w-full md:w-1/2">
                        <textarea id="securityLeadComments" name="securityLeadComments" rows="2" placeholder="Ghi chú của Trưởng bộ phận Bảo mật (nếu có)" class="mt-2"></textarea>
                    </div>
                </div>
            </section>

            <div class="mt-10 text-right">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md transition duration-150 ease-in-out">
                    Gửi Yêu Cầu
                </button>
            </div>
        </form>
    </div>

    <script>
        // Hàm thêm hàng mới vào bảng tài sản
        function addAssetRow() {
            const tableBody = document.getElementById('assetsTableBody');
            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td class="border border-gray-300 px-2 py-1">
                    <input type="text" name="assetName[]" placeholder="Ví dụ: Server Web App" class="w-full border-0 p-1 focus:ring-1 focus:ring-blue-500">
                </td>
                <td class="border border-gray-300 px-2 py-1">
                    <input type="text" name="assetIP[]" placeholder="************" class="w-full border-0 p-1 focus:ring-1 focus:ring-blue-500">
                </td>
                <td class="border border-gray-300 px-2 py-1">
                    <input type="text" name="assetVersion[]" placeholder="Apache 2.4.50" class="w-full border-0 p-1 focus:ring-1 focus:ring-blue-500">
                </td>
                <td class="border border-gray-300 px-2 py-1">
                    <input type="text" name="assetNotes[]" placeholder="Ghi chú thêm" class="w-full border-0 p-1 focus:ring-1 focus:ring-blue-500">
                </td>
                <td class="border border-gray-300 px-2 py-1 text-center">
                    <button type="button" onclick="removeAssetRow(this)" class="text-red-600 hover:text-red-800 font-medium">Xóa</button>
                </td>
            `;
            tableBody.appendChild(newRow);
        }

        // Hàm xóa hàng khỏi bảng tài sản
        function removeAssetRow(button) {
            const tableBody = document.getElementById('assetsTableBody');
            // Chỉ cho phép xóa nếu còn nhiều hơn 1 hàng
            if (tableBody.children.length > 1) {
                button.closest('tr').remove();
            } else {
                alert('Phải có ít nhất một tài sản trong danh sách.');
            }
        }

        // Script xử lý form
        const form = document.getElementById('securityExceptionForm');
        form.addEventListener('submit', function(event) {
            event.preventDefault(); // Ngăn chặn việc gửi form theo cách truyền thống

            // Thu thập dữ liệu từ bảng tài sản
            const assetNames = Array.from(document.querySelectorAll('input[name="assetName[]"]')).map(input => input.value);
            const assetIPs = Array.from(document.querySelectorAll('input[name="assetIP[]"]')).map(input => input.value);
            const assetVersions = Array.from(document.querySelectorAll('input[name="assetVersion[]"]')).map(input => input.value);
            const assetNotes = Array.from(document.querySelectorAll('input[name="assetNotes[]"]')).map(input => input.value);

            // Tạo danh sách tài sản
            const affectedAssets = [];
            for (let i = 0; i < assetNames.length; i++) {
                if (assetNames[i] || assetIPs[i] || assetVersions[i]) {
                    affectedAssets.push({
                        name: assetNames[i],
                        ip: assetIPs[i],
                        version: assetVersions[i],
                        notes: assetNotes[i]
                    });
                }
            }

            // Xử lý dữ liệu form
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            data.affectedAssets = affectedAssets;

            console.log('Dữ liệu form:', data);
            alert('Yêu cầu đã được gửi (xem console để biết chi tiết dữ liệu).');
        });
    </script>
</body>
</html>
